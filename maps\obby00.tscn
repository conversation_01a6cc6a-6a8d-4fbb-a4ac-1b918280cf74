[gd_scene load_steps=15 format=3 uid="uid://bd14lbdka4hx0"]

[ext_resource type="AudioStream" uid="uid://cgxpsuuornpff" path="res://assets/snd/music/doyouknowwhereyourecomingfrominstrumental.wav" id="1_attg6"]
[ext_resource type="AudioStream" uid="uid://da4yrjk2hwqp8" path="res://assets/snd/ambient/sound_ambient_ambience_urban_rooftop_ambloop02.wav" id="2_pr3j4"]
[ext_resource type="Environment" uid="uid://dcraioyqyti3s" path="res://assets/env/obby00.tres" id="3_2kmb1"]
[ext_resource type="Script" uid="uid://cw71vo1m4dwjr" path="res://resource/scripts/respawn.gd" id="4_7mnov"]
[ext_resource type="Material" uid="uid://7to3kb1tisdd" path="res://addons/prototype_mini_bundle/M_prototype_orange.tres" id="5_tntpv"]
[ext_resource type="Script" uid="uid://ckcg4im8r7b72" path="res://resource/entities/killbox.gd" id="6_vi03j"]
[ext_resource type="Material" uid="uid://1nvrnylcajfg" path="res://addons/prototype_mini_bundle/M_prototype_red.tres" id="7_xpnrq"]
[ext_resource type="PackedScene" uid="uid://b4hopyks1f1c1" path="res://resource/entities/player/ss_player.tscn" id="8_d6dxo"]
[ext_resource type="PackedScene" uid="uid://bseg4x8xy8m0b" path="res://resource/entities/goal.tscn" id="9_pctli"]
[ext_resource type="Script" uid="uid://biouiuovekqto" path="res://resource/entities/goal_routines/goal_routine_simple.gd" id="10_pr3j4"]

[sub_resource type="BoxShape3D" id="BoxShape3D_sirwh"]
size = Vector3(39, 1, 102)

[sub_resource type="BoxShape3D" id="BoxShape3D_hkkw6"]
size = Vector3(105, 1, 44)

[sub_resource type="BoxMesh" id="BoxMesh_61s1m"]

[sub_resource type="BoxShape3D" id="BoxShape3D_61s1m"]

[node name="Node3D" type="Node3D"]

[node name="env" type="Node" parent="."]

[node name="lights" type="Node" parent="env"]

[node name="bgm" type="AudioStreamPlayer" parent="env"]
stream = ExtResource("1_attg6")
volume_db = -2.476
mix_target = 2
bus = &"bgm"

[node name="env_ambience" type="AudioStreamPlayer" parent="env"]
stream = ExtResource("2_pr3j4")
volume_db = -9.705
autoplay = true
max_polyphony = 5

[node name="WorldEnvironment" type="WorldEnvironment" parent="env"]
environment = ExtResource("3_2kmb1")

[node name="respawn" type="Node" parent="env"]
script = ExtResource("4_7mnov")

[node name="world" type="Node3D" parent="."]

[node name="start" type="CSGBox3D" parent="world"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -2, 0)
material_override = ExtResource("5_tntpv")
use_collision = true
size = Vector3(17.4763, 0.241211, 18.9083)

[node name="killbox" type="Node" parent="world" node_paths=PackedStringArray("kill_areas")]
script = ExtResource("6_vi03j")
kill_areas = [NodePath("Area3D"), NodePath("Area3D2")]

[node name="Area3D" type="Area3D" parent="world/killbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -11, -41)
collision_layer = 2
collision_mask = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/killbox/Area3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3.57628e-07, 0, -0.5)
shape = SubResource("BoxShape3D_sirwh")

[node name="Area3D2" type="Area3D" parent="world/killbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, -11, -41)
collision_layer = 2
collision_mask = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/killbox/Area3D2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -33, 0, -29.5)
shape = SubResource("BoxShape3D_hkkw6")

[node name="wall" type="CSGBox3D" parent="world"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15.2619, -1.12061, -41.0459)
visible = false
material_override = ExtResource("5_tntpv")
use_collision = true
size = Vector3(8, 20, 101)

[node name="wall2" type="CSGBox3D" parent="world"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14.7381, -1.12061, -19.5459)
visible = false
material_override = ExtResource("5_tntpv")
use_collision = true
size = Vector3(8, 20, 58)

[node name="wall3" type="CSGBox3D" parent="world"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -47.7382, -1.12061, -53.5459)
visible = false
material_override = ExtResource("5_tntpv")
use_collision = true
size = Vector3(74, 20, 10)

[node name="wall4" type="CSGBox3D" parent="world"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36.2382, -1.12061, -95.5459)
visible = false
material_override = ExtResource("5_tntpv")
use_collision = true
size = Vector3(97, 20, 10)

[node name="CSGBox3D2" type="CSGBox3D" parent="world"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -2, -10)
use_collision = true
size = Vector3(17.4763, 0.241211, 1.15286)

[node name="MeshInstance3D" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, 0, -2, -17)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D2" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, 0, -2, -25)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D2"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D2/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, 0, -2, -34)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D3"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D3/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D4" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, 0, -2, -50)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D4"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D4/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D6" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, -14, -2, -66)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D6"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D6/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D11" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, -46, -2, -66)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D11"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D11/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D9" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, -14, -2, -80)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D9"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D9/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D13" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, -74, -2, -80)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D13"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D13/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D5" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, 0, 4, -42)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D5"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D5/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D7" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, 0, 4, -59)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D7"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D7/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D8" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, 0, 4, -73)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D8"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D8/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D10" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, -31, 4, -73)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D10"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D10/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="MeshInstance3D12" type="MeshInstance3D" parent="world"]
transform = Transform3D(17.34, 0, 0, 0, 0.3, 0, 0, 0, 5.9, -60, 4, -73)
mesh = SubResource("BoxMesh_61s1m")
surface_material_override/0 = ExtResource("5_tntpv")

[node name="StaticBody3D" type="StaticBody3D" parent="world/MeshInstance3D12"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="world/MeshInstance3D12/StaticBody3D"]
shape = SubResource("BoxShape3D_61s1m")

[node name="hurtbox" type="CSGBox3D" parent="world"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.26185, -11, -41.0459)
material_override = ExtResource("7_xpnrq")
size = Vector3(38, 0.241211, 101)

[node name="hurtbox2" type="CSGBox3D" parent="world/hurtbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -65.7855, 0, -29.4541)
material_override = ExtResource("7_xpnrq")
use_collision = true
size = Vector3(94, 0.241211, 44)

[node name="ss_player" parent="." instance=ExtResource("8_d6dxo")]

[node name="respawn hud" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 256.0
offset_bottom = 40.0

[node name="Label" type="Label" parent="respawn hud"]
modulate = Color(1, 1, 1, 0.519)
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Press Q to respawn."

[node name="GoalObject" parent="." instance=ExtResource("9_pctli")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -80.4251, 0, -80.1083)

[node name="GoalRoutine" type="Node" parent="GoalObject"]
script = ExtResource("10_pr3j4")
