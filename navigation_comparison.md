# 2D vs 3D Navigation Setup Comparison

## Key Differences for 3D Enemy Avoidance

### Node Types
- **2D**: `NavigationAgent2D`, `NavigationObstacle2D`, `NavigationRegion2D`
- **3D**: `NavigationAgent3D`, `NavigationObstacle3D`, `NavigationRegion3D`

### Movement Vectors
- **2D**: `Vector2(x, y)` - simple 2D movement
- **3D**: `Vector3(x, y, z)` - must handle gravity separately

### Gravity Handling
```gdscript
# 2D - No gravity concerns
velocity = safe_velocity
move_and_slide()

# 3D - Preserve gravity in Y axis
velocity.x = safe_velocity.x
velocity.z = safe_velocity.z
# Keep existing Y velocity for gravity
move_and_slide()
```

### Agent Configuration
```gdscript
# 2D Agent
navigation_agent.radius = 16.0  # pixels
navigation_agent.max_speed = 150.0

# 3D Agent  
navigation_agent.radius = 0.5   # meters
navigation_agent.height = 1.8   # important for 3D!
navigation_agent.max_speed = 150.0
```

### Obstacle Setup
```gdscript
# 2D Obstacle
nav_obstacle.radius = 32.0
# OR
nav_obstacle.vertices = PackedVector2Array([...])

# 3D Obstacle
nav_obstacle.radius = 1.0
nav_obstacle.height = 2.0  # height is crucial
# OR  
nav_obstacle.vertices = PackedVector3Array([...])
```

### Rotation/Facing Direction
```gdscript
# 2D - Simple rotation
if velocity.length() > 0.1:
    rotation = velocity.angle()

# 3D - Y-axis rotation for facing direction
if Vector2(velocity.x, velocity.z).length() > 0.1:
    var look_direction = Vector2(velocity.x, velocity.z).normalized()
    rotation.y = atan2(-look_direction.x, -look_direction.y)
```

## Scene Structure for 3D Enemy

```
Main (Node3D)
├── NavigationRegion3D
│   ├── MeshInstance3D (floor)
│   └── StaticBody3D (floor collision)
├── Walls (StaticBody3D)
│   ├── CollisionShape3D
│   ├── MeshInstance3D  
│   └── NavigationObstacle3D
├── QuakeKnight3D (CharacterBody3D)
│   ├── NavigationAgent3D
│   ├── CollisionShape3D (CapsuleShape3D)
│   ├── MeshInstance3D
│   └── AnimationPlayer (optional)
└── Player (CharacterBody3D)
    ├── CollisionShape3D
    └── MeshInstance3D
```

## Important 3D Considerations

1. **Height Matters**: Always set `height` on NavigationAgent3D and NavigationObstacle3D
2. **Gravity Separation**: Handle gravity separately from horizontal movement
3. **Y-Axis Rotation**: Use `rotation.y` for character facing direction
4. **Navigation Mesh**: Must be baked for 3D navigation to work
5. **Agent Radius**: Use realistic meter-based values, not pixel values

## Performance Tips for 3D

1. **Limit Active Agents**: Only enable avoidance for nearby enemies
2. **LOD System**: Reduce AI complexity for distant enemies  
3. **Update Frequency**: Use lower update rates for far enemies
4. **Neighbor Limits**: Set reasonable `max_neighbors` values
5. **Avoidance Radius**: Don't make radii too large

## Common 3D Issues

1. **Floating Enemies**: Ensure gravity is properly applied
2. **Stuck on Slopes**: Configure `agent_max_slope` correctly
3. **No Navigation**: NavigationMesh must be baked
4. **Jittery Movement**: Smooth rotation with `lerp_angle()`
5. **Performance**: Too many active agents causes frame drops
