extends CharacterBody3D
class_name Quake<PERSON>night3D

# Quake-style AI states
enum AIState { 
	STAND, 
	WALK, 
	RUN, 
	MELEE_ATTACK, 
	PAIN, 
	DEATH 
}

# Movement and combat properties
@export var movement_speed: float = 150.0
@export var walk_speed: float = 75.0
@export var health: int = 75
@export var attack_damage: int = 20
@export var attack_range: float = 2.0
@export var sight_range: float = 15.0
@export var gravity: float = 9.8

# AI state management
var current_state: AIState = AIState.STAND
var target_player: Node3D
var last_known_player_position: Vector3
var state_timer: float = 0.0
var attack_cooldown: float = 0.0

# Navigation components
@onready var navigation_agent: NavigationAgent3D = $NavigationAgent3D
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D
@onready var collision_shape: CollisionShape3D = $CollisionShape3D

# Animation and visual components (optional)
@onready var animation_player: AnimationPlayer = $AnimationPlayer if has_node("AnimationPlayer") else null

func _ready():
	# Configure NavigationAgent3D for avoidance
	navigation_agent.avoidance_enabled = true
	navigation_agent.radius = 0.5  # Half-meter radius
	navigation_agent.height = 1.8   # Human-like height
	navigation_agent.max_speed = movement_speed
	navigation_agent.path_desired_distance = 1.0
	navigation_agent.target_desired_distance = 1.5
	navigation_agent.path_max_distance = 3.0
	
	# Avoidance settings for crowd behavior
	navigation_agent.neighbor_distance = 3.0
	navigation_agent.max_neighbors = 5
	navigation_agent.time_horizon = 1.5
	
	# Connect velocity computed signal
	navigation_agent.velocity_computed.connect(_on_velocity_computed)
	
	# Find player reference
	target_player = get_tree().get_first_node_in_group("player")
	
	# Start in stand state
	change_state(AIState.STAND)

func _physics_process(delta):
	# Update timers
	state_timer += delta
	attack_cooldown = max(0.0, attack_cooldown - delta)
	
	# Apply gravity
	if not is_on_floor():
		velocity.y -= gravity * delta
	
	# Check for player and update AI
	update_ai_logic(delta)
	
	# Execute current state behavior
	execute_current_state()

func update_ai_logic(_delta):
	# Check if player is in sight range
	if target_player and current_state != AIState.DEATH:
		var distance_to_player = global_position.distance_to(target_player.global_position)
		
		# Line of sight check (simplified)
		if distance_to_player <= sight_range and can_see_player():
			last_known_player_position = target_player.global_position
			
			# State transitions based on distance and conditions
			if distance_to_player <= attack_range and attack_cooldown <= 0.0:
				if current_state != AIState.MELEE_ATTACK:
					change_state(AIState.MELEE_ATTACK)
			elif distance_to_player <= sight_range:
				if current_state == AIState.STAND or current_state == AIState.WALK:
					change_state(AIState.RUN)

func can_see_player() -> bool:
	if not target_player:
		return false
	
	# Simple line-of-sight check using raycast
	var space_state = get_world_3d().direct_space_state
	var query = PhysicsRayQueryParameters3D.create(
		global_position + Vector3(0, 1, 0),  # From enemy eye level
		target_player.global_position + Vector3(0, 1, 0)  # To player eye level
	)
	query.exclude = [self]
	
	var result = space_state.intersect_ray(query)
	return result.is_empty() or result.collider == target_player

func change_state(new_state: AIState):
	# Exit current state
	match current_state:
		AIState.MELEE_ATTACK:
			pass  # Clean up attack state
	
	# Enter new state
	current_state = new_state
	state_timer = 0.0
	
	# Play appropriate animation
	if animation_player:
		match new_state:
			AIState.STAND:
				animation_player.play("stand")
			AIState.WALK:
				animation_player.play("walk")
			AIState.RUN:
				animation_player.play("run")
			AIState.MELEE_ATTACK:
				animation_player.play("attack")
			AIState.PAIN:
				animation_player.play("pain")
			AIState.DEATH:
				animation_player.play("death")

func execute_current_state():
	match current_state:
		AIState.STAND:
			ai_stand()
		AIState.WALK:
			ai_walk()
		AIState.RUN:
			ai_run()
		AIState.MELEE_ATTACK:
			ai_melee_attack()
		AIState.PAIN:
			ai_pain()
		AIState.DEATH:
			ai_death()

func ai_stand():
	# Stop movement
	navigation_agent.set_velocity(Vector3.ZERO)
	
	# Randomly transition to walk after some time
	if state_timer > randf_range(2.0, 5.0):
		change_state(AIState.WALK)

func ai_walk():
	# Patrol behavior - move to random nearby points
	if not navigation_agent.is_navigation_finished():
		move_towards_target(walk_speed)
	else:
		# Pick a new random destination
		var random_point = global_position + Vector3(
			randf_range(-5.0, 5.0),
			0,
			randf_range(-5.0, 5.0)
		)
		navigation_agent.target_position = random_point
	
	# Chance to return to standing
	if state_timer > randf_range(3.0, 8.0):
		change_state(AIState.STAND)

func ai_run():
	# Chase the player
	if target_player:
		navigation_agent.target_position = last_known_player_position
		move_towards_target(movement_speed)
	
	# If we lose the player, go back to patrolling
	if state_timer > 10.0:  # Give up after 10 seconds
		change_state(AIState.WALK)

func ai_melee_attack():
	# Stop moving during attack
	navigation_agent.set_velocity(Vector3.ZERO)
	
	# Face the player
	if target_player:
		look_at_target(target_player.global_position)
	
	# Execute attack after animation windup
	if state_timer > 0.5 and state_timer < 0.6:  # Attack frame
		perform_melee_attack()
	
	# Return to running after attack completes
	if state_timer > 1.0:
		attack_cooldown = 2.0  # 2 second cooldown
		change_state(AIState.RUN)

func ai_pain():
	# Brief stun state
	navigation_agent.set_velocity(Vector3.ZERO)
	
	if state_timer > 0.5:
		change_state(AIState.RUN)

func ai_death():
	# Stop all movement
	navigation_agent.set_velocity(Vector3.ZERO)
	# Disable collision after death animation
	if state_timer > 2.0:
		collision_shape.disabled = true

func move_towards_target(speed: float):
	if navigation_agent.is_navigation_finished():
		return
	
	var next_pos = navigation_agent.get_next_path_position()
	var direction = (next_pos - global_position).normalized()
	
	# Create horizontal movement vector (preserve Y for gravity)
	var desired_velocity = Vector3(direction.x, 0, direction.z) * speed
	
	# Submit to avoidance system
	navigation_agent.set_velocity(desired_velocity)

func _on_velocity_computed(safe_velocity: Vector3):
	# Apply the safe velocity while preserving gravity
	velocity.x = safe_velocity.x
	velocity.z = safe_velocity.z
	# Don't override Y velocity (gravity is handled separately)
	
	move_and_slide()
	
	# Rotate to face movement direction (Quake-style)
	if Vector2(velocity.x, velocity.z).length() > 0.1:
		var look_direction = Vector2(velocity.x, velocity.z).normalized()
		var target_rotation = atan2(-look_direction.x, -look_direction.y)
		rotation.y = lerp_angle(rotation.y, target_rotation, 0.1)

func look_at_target(target_pos: Vector3):
	var direction = (target_pos - global_position).normalized()
	var target_rotation = atan2(-direction.x, -direction.z)
	rotation.y = lerp_angle(rotation.y, target_rotation, 0.2)

func perform_melee_attack():
	if not target_player:
		return
	
	var distance = global_position.distance_to(target_player.global_position)
	if distance <= attack_range:
		# Deal damage to player
		if target_player.has_method("take_damage"):
			target_player.take_damage(attack_damage)
		print("Knight attacks for ", attack_damage, " damage!")

func take_damage(amount: int):
	health -= amount
	
	if health <= 0:
		change_state(AIState.DEATH)
	else:
		# Brief pain state
		change_state(AIState.PAIN)
	
	print("Knight takes ", amount, " damage! Health: ", health)

# Utility function to set up the enemy in the scene
func setup_knight():
	# This can be called to configure the knight after instantiation
	if not navigation_agent:
		navigation_agent = NavigationAgent3D.new()
		add_child(navigation_agent)
	
	# Ensure we have basic collision
	if not collision_shape:
		collision_shape = CollisionShape3D.new()
		var capsule = CapsuleShape3D.new()
		capsule.radius = 0.5
		capsule.height = 1.8
		collision_shape.shape = capsule
		add_child(collision_shape)
	
	# Basic mesh if none exists
	if not mesh_instance:
		mesh_instance = MeshInstance3D.new()
		var capsule_mesh = CapsuleMesh.new()
		capsule_mesh.radius = 0.5
		capsule_mesh.height = 1.8
		mesh_instance.mesh = capsule_mesh
		add_child(mesh_instance)
