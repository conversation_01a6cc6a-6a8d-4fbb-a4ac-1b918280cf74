extends CharacterBody3D

enum AIState { STAND, WALK, RUN, ATTACK }
var current_state = AIState.STAND
var target_player: Node3D

@export var movement_speed: float = 150.0
@export var gravity: float = 9.8
@onready var navigation_agent: NavigationAgent3D = $NavigationAgent3D

func _ready():
    # Configure for Quake-style movement
    navigation_agent.avoidance_enabled = true
    navigation_agent.radius = 0.5  # 3D uses meters, not pixels
    navigation_agent.height = 1.8  # Enemy height for 3D navigation
    navigation_agent.max_speed = movement_speed
    navigation_agent.path_desired_distance = 1.0
    navigation_agent.target_desired_distance = 1.5

    # Connect velocity computed signal
    navigation_agent.velocity_computed.connect(_on_velocity_computed)

func _physics_process(delta):
    # Apply gravity
    if not is_on_floor():
        velocity.y -= gravity * delta

    # Handle AI movement
    match current_state:
        AIState.RUN:
            ai_run()
        AIState.WALK:
            ai_walk()
        AIState.STAND:
            ai_stand()

func ai_run():
    if target_player:
        navigation_agent.target_position = target_player.global_position

        if not navigation_agent.is_navigation_finished():
            var next_pos = navigation_agent.get_next_path_position()
            var direction = (next_pos - global_position).normalized()
            # Only use X and Z for horizontal movement, preserve Y for gravity
            var desired_velocity = Vector3(direction.x, 0, direction.z) * movement_speed
            navigation_agent.set_velocity(desired_velocity)
        else:
            navigation_agent.set_velocity(Vector3.ZERO)

func ai_walk():
    # Similar to ai_run but slower
    if target_player:
        navigation_agent.target_position = target_player.global_position

        if not navigation_agent.is_navigation_finished():
            var next_pos = navigation_agent.get_next_path_position()
            var direction = (next_pos - global_position).normalized()
            var desired_velocity = Vector3(direction.x, 0, direction.z) * (movement_speed * 0.5)
            navigation_agent.set_velocity(desired_velocity)
        else:
            navigation_agent.set_velocity(Vector3.ZERO)

func ai_stand():
    navigation_agent.set_velocity(Vector3.ZERO)

func _on_velocity_computed(safe_velocity: Vector3):
    # Apply the safe velocity while preserving gravity
    velocity.x = safe_velocity.x
    velocity.z = safe_velocity.z
    # Don't override Y velocity (gravity)

    move_and_slide()

    # Update enemy rotation to face movement direction (Quake-style)
    if Vector2(velocity.x, velocity.z).length() > 0.1:
        var look_direction = Vector2(velocity.x, velocity.z).normalized()
        rotation.y = atan2(-look_direction.x, -look_direction.y)