extends Node

# GameMaster.gd – controls round flow for survival maps like "Belltower".
# Enhanced version with MusicManager integration for dynamic music sets.

signal game_started                             # emitted once bell rings
signal game_over(survived_time_ms, kills)       # emitted when player dies

@export var bell_path: NodePath
@export var player_path: NodePath
@export var ui_timer_label: NodePath
@export var ui_ms_label: NodePath       # optional label to show centiseconds
@export var ui_kills_label: NodePath
@export var ui_doom_label: NodePath       # optional label to show doom countdown seconds
@export var enemy_spawn_zones_path: NodePath # New export variable for enemy spawn zones
@export var bgm_node_path: NodePath = NodePath("env/bgm")  # Path to the BGM AudioStreamPlayer

# Music Manager Integration
@export var music_manager: MusicManager
@export var auto_randomize_music: bool = true

# Overwhelm UI & audio
@export var ui_overwhelm_meter: NodePath  # ProgressBar / TextureProgress showing enemy count
@export var doom_tick_sound: AudioStream  # Sound played every second while doom timer counts
@export var doom_zero_sound: AudioStream  # Sound played when countdown hits zero
@export var in_game_bgm_stream: AudioStream  # BGM to switch to when bell rings (in-game music) - LEGACY

# --- Reaper parameters ---------------------------------------------------
@export var reaper_threshold_speed : float = 100.0 # Speed below which reaper timer counts
@export var reaper_time : float = 2.0              # Seconds before instant death
@export var reaper_color : Color = Color(1,0,0,0.6) # Target screen tint color
@export var ui_reaper_overlay : NodePath           # ColorRect or Control to tint screen
@export var reaper_sound : AudioStream             # Sound played when reaper strikes
@export var overwhelm_bgm_stream : AudioStream      # Optional BGM to play once doom finishes - LEGACY

# Audio fade settings
const BGM_BUS_NAME := "bgm"
var _bgm_bus_idx : int = -1
const BGM_ORIGINAL_DB : float = 0.0
var _bgm_tween : Tween
var _original_bgm_stream : AudioStream = null
var _current_bgm_stream : AudioStream = null # Tracks the music that should play when not in overwhelm

enum State { WARMUP, RUNNING, GAME_OVER }

var _state : State = State.WARMUP
var _start_time_ms : int = 0
var _kills : int = 0
var _ticker : Timer
var _spawn_timer : Timer # New timer for enemy spawning
var _spawn_interval : float = 40.0 # Initial spawn interval of 40 seconds
var _interval_decrease_timer : Timer # Timer to decrease spawn interval
var _player_connected : bool = false

# Helper to find a descendant that has any of the given signals
func _find_node_with_signal(root: Node, signal_names: Array[String]) -> Node:
	if not root:
		return null
	for sig in signal_names:
		if root.has_signal(sig):
			return root
	# search children
	for child in root.get_children():
		var found = _find_node_with_signal(child, signal_names)
		if found:
			return found
	return null

# --- Overwhelm parameters -----------------------------------------------
@export var overwhelm_limit : int = 30   # Max enemies allowed before doom
@export var doom_countdown : float = 5.0 # Seconds before charge starts
@export var overwhelm_speed_multiplier : float = 6.0 # Speed multiplier applied to enemies during overwhelm
@export var global_enemy_cap : int = 80   # Hard limit on number of enemies in the scene
@export var max_spawners : int = 3        # Maximum concurrent enemy spawners
var _doom_timer : Timer
var _doom_pending : bool = false
var _doom_active  : bool = false

var _tick_timer : Timer         # 1-second interval for countdown beeps
var _tick_player : AudioStreamPlayer
var _zero_player : AudioStreamPlayer
# Reaper internals
var _reaper_elapsed : float = 0.0
var _overlay_original_color : Color = Color(0,0,0,0)
const _UPS_FACTOR : float = 39.37 # meters/sec to Quake-style units/sec

# Overwhelm meter pulse helpers
var _meter_original_modulate : Color = Color(1,1,1,1)
var _meter_pulse_tween : Tween
# Doom label animation helpers
var _prev_doom_value : int = -999
var _doom_label_tween : Tween

# --- Wave control ---------------------------------------------------------
var _wave : int = 0           # Current wave number (first wave = 1)
# --- Simple kill tracking -----------------------------------------------
var _enemy_set : Dictionary = {}

func _ready():
	_ticker = Timer.new()
	_ticker.wait_time = 0.05   # update 20 Hz for smooth ms display
	_ticker.timeout.connect(_on_tick)
	add_child(_ticker)

	# Initialize spawn timer
	_spawn_timer = Timer.new()
	_spawn_timer.timeout.connect(_on_spawn_timer_timeout)
	add_child(_spawn_timer)

	# Initialize interval decrease timer
	_interval_decrease_timer = Timer.new()
	_interval_decrease_timer.wait_time = 30.0 # Decrease every 30 seconds
	_interval_decrease_timer.timeout.connect(_on_interval_decrease_timeout)
	add_child(_interval_decrease_timer)

	# Doom timer setup
	_doom_timer = Timer.new()
	_doom_timer.one_shot = true
	_doom_timer.wait_time = doom_countdown
	_doom_timer.timeout.connect(_on_doom_timer_timeout)
	add_child(_doom_timer)

	# Tick timer and audio player for doom countdown
	_tick_timer = Timer.new()
	_tick_timer.wait_time = 1.0
	_tick_timer.one_shot = false
	_tick_timer.timeout.connect(_on_tick_timer_timeout)
	add_child(_tick_timer)

	_tick_player = AudioStreamPlayer.new()
	_tick_player.stream = doom_tick_sound
	add_child(_tick_player)

	# Cache bgm bus index and original volume
	_bgm_bus_idx = AudioServer.get_bus_index(BGM_BUS_NAME)
	if _bgm_bus_idx >= 0:
		pass # No longer caching original volume

	# Cache original BGM stream if autoload present
	var bgm = get_node_or_null(bgm_node_path)
	if bgm:
		if bgm and "stream" in bgm:
			_original_bgm_stream = bgm.stream
			_current_bgm_stream = bgm.stream

	# Add to global group for other systems to query (e.g., grappling hook)
	add_to_group("gamemaster")

	_zero_player = AudioStreamPlayer.new()
	_zero_player.stream = doom_zero_sound
	add_child(_zero_player)

	# Listen for new nodes so we can apply overwhelm effects to late spawns
	get_tree().node_added.connect(_on_node_added)

	# Bell hookup
	if bell_path != NodePath("") and has_node(bell_path):
		var bell = get_node(bell_path)
		if bell.has_signal("bell_rang"):
			bell.connect("bell_rang", _on_bell_rang)
		else:
			push_warning("Bell has no bell_rang signal – game will not start")
	else:
		push_warning("bell_path not assigned")

	connect("game_started", _on_game_started) # Connect to game_started signal
	# Defer player signal hookup to ensure entire scene tree is ready
	call_deferred("_setup_player_connection")
	
	# Initialize music manager if available
	_initialize_music_manager()

func _initialize_music_manager():
	if music_manager:
		print("[GameMaster] Music manager found - initializing music system")
		# Connect to music manager signals for debugging
		music_manager.music_changed.connect(_on_music_changed)
		music_manager.music_set_changed.connect(_on_music_set_changed)
		
		# Select random music set if enabled
		if auto_randomize_music:
			music_manager.select_random_music_set()
	else:
		print("[GameMaster] No music manager assigned - using legacy BGM system")

# -------- State transitions -------------------------------------------------
func _switch_to_in_game_bgm():
	print("[GameMaster] _switch_to_in_game_bgm called")
	await get_tree().create_timer(0.5).timeout
	
	# Use MusicManager if available, otherwise fall back to legacy system
	if music_manager:
		print("[GameMaster] Using MusicManager for in-game music")
		music_manager.play_state_music(MusicManager.GameState.NORMAL_GAME)
	else:
		# Legacy BGM system
		var bgm_node = get_node_or_null(bgm_node_path)
		print("[GameMaster] BGM node found: ", bgm_node)
		print("[GameMaster] In-game BGM stream: ", in_game_bgm_stream.resource_path if in_game_bgm_stream else "null")
		if bgm_node and bgm_node is AudioStreamPlayer:
			bgm_node.stream = in_game_bgm_stream
			bgm_node.play()
			_current_bgm_stream = in_game_bgm_stream
			print("[GameMaster] Switched to in-game BGM: ", in_game_bgm_stream.resource_path)
		else:
			print("[GameMaster] Unable to find BGM player at env/bgm")

func _on_bell_rang():
	if _state != State.WARMUP:
		return
	print("[GameMaster] Bell rang - switching to in-game BGM")
	_state = State.RUNNING
	_start_time_ms = Time.get_ticks_msec()
	_kills = 0
	_ticker.start()
	# Switch to in-game BGM after a short delay (like BellController did)
	call_deferred("_switch_to_in_game_bgm")
	emit_signal("game_started")
	_update_ui()

func _on_game_started():
	# Start spawning enemies
	if enemy_spawn_zones_path != NodePath("") and has_node(enemy_spawn_zones_path):
		var spawn_zones_node = get_node(enemy_spawn_zones_path)
		if spawn_zones_node.get_child_count() > 0:
			_spawn_timer.wait_time = 0.0 # Trigger first wave immediately
			_on_spawn_timer_timeout()    # Wave 1
			# Prepare timer for subsequent waves (until wave 5)
			if _wave < 5:
				_spawn_timer.wait_time = _spawn_interval
				_spawn_timer.start()
			_interval_decrease_timer.start()
		else:
			push_warning("No Area3D nodes found under enemy_spawn_zones_path")
	else:
		push_warning("enemy_spawn_zones_path not assigned or not found")

# ... existing code continues with all the same functionality ...
# (I'll continue with the rest of the methods, but for brevity I'll show the key music integration points)

func _on_doom_timer_timeout():
	print("[GameMaster] Doom countdown expired – enemies charging!")
	_doom_active = true
	_doom_pending = false
	_tick_timer.stop()
	# Apply overwhelm effects to all existing enemies
	var player_node: Node3D = null
	if player_path != NodePath("") and has_node(player_path):
		player_node = get_node(player_path)
	for enemy in get_tree().get_nodes_in_group("enemy"):
		_apply_overwhelm_to_enemy(enemy, player_node)

	# Switch to doom/overwhelm music using MusicManager if available
	if music_manager:
		print("[GameMaster] Using MusicManager for doom music")
		music_manager.play_state_music(MusicManager.GameState.DOOM)
	else:
		# Legacy overwhelm BGM system
		var bgm_node: Node = null
		bgm_node = get_node_or_null(bgm_node_path)

		if overwhelm_bgm_stream and bgm_node:
			# Force-switch to the overwhelm track
			bgm_node.stop()               # halt any currently playing music
			bgm_node.stream = overwhelm_bgm_stream
			# Force the stream to take effect
			bgm_node.stream_paused = false
			bgm_node.process_mode = Node.PROCESS_MODE_INHERIT
			bgm_node.play()               # start the new track immediately
			# Fade bus back up to original volume over 1 second
			AudioServer.set_bus_volume_db(_bgm_bus_idx, 0.0) # Directly set to 0 dB
		else:
			# No custom overwhelm track; keep the bus muted so the previous track stays silent
			AudioServer.set_bus_volume_db(_bgm_bus_idx, 0.0) # Directly set to 0 dB

	# Play zero sound
	if doom_zero_sound and _zero_player:
		_zero_player.play()

	_update_doom_label(0)

func _on_player_died():
	if _state != State.RUNNING:
		return
	# Stop doom timer if active
	if _doom_timer and _doom_timer.is_stopped() == false:
		_doom_timer.stop()
	if _tick_timer and _tick_timer.is_stopped() == false:
		_tick_timer.stop()
	_doom_active = false
	_doom_pending = false
	
	# Switch back to pre-game music using MusicManager if available
	if music_manager:
		print("[GameMaster] Using MusicManager for pre-game music")
		music_manager.play_state_music(MusicManager.GameState.PRE_GAME)
	else:
		# Legacy BGM restoration
		# Restore BGM volume/state if it was lowered by the doom mechanic
		if _bgm_bus_idx >= 0:
			_fade_bgm_to(BGM_ORIGINAL_DB, 0.5)
		# If we switched to the overwhelm music stream, revert to the original
		var bgm = get_node_or_null(bgm_node_path)
		if bgm and _current_bgm_stream:
			if bgm:
				bgm.stream = _current_bgm_stream
				if not bgm.playing:
					bgm.play()
	
	_state = State.GAME_OVER
	_ticker.stop()
	_spawn_timer.stop() # Stop the enemy spawn timer
	_interval_decrease_timer.stop() # Stop the interval decrease timer
	var elapsed_ms := Time.get_ticks_msec() - _start_time_ms
	emit_signal("game_over", elapsed_ms, _kills)
	_show_game_over(elapsed_ms)

# Music Manager event handlers
func _on_music_changed(track_name: String, state: MusicManager.GameState):
	print("[GameMaster] Music changed to: ", track_name, " (State: ", state, ")")

func _on_music_set_changed(set_name: String):
	print("[GameMaster] Music set changed to: ", set_name)

# Public methods for manual music control
func select_random_music_set():
	if music_manager:
		music_manager.select_random_music_set()

func trigger_doom_music():
	if music_manager:
		music_manager.play_state_music(MusicManager.GameState.DOOM)

func trigger_overwhelm_music():
	if music_manager:
		music_manager.play_state_music(MusicManager.GameState.OVERWHELM)

# ... rest of the existing GameMaster methods would continue here ...
# (For brevity, I'm showing the key integration points. The full implementation
# would include all the existing methods from the original GameMaster) 