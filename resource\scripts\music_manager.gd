class_name MusicManager
extends Node

## Manages music transitions between different game states
## Handles random selection of music sets and smooth transitions

signal music_changed(track_name: String, state: GameState)
signal music_set_changed(set_name: String)

enum GameState {
	PRE_GAME,
	NORMAL_GAME,
	DOOM,
	OVERWHELM
}

@export var music_sets: Array[MusicSet] = []
@export var fade_duration: float = 1.0
@export var auto_randomize: bool = true

var _current_music_set: MusicSet
var _current_state: GameState = GameState.PRE_GAME
var _bgm_player: AudioStreamPlayer
var _fade_tween: Tween

func _ready():
	# Find the BGM player in the scene
	_bgm_player = _find_bgm_player()
	if not _bgm_player:
		push_warning("MusicManager: No BGM player found in scene")
		return
	
	# Initialize with a random music set if available
	if auto_randomize and music_sets.size() > 0:
		select_random_music_set()
	
	# Start with pre-game music
	play_state_music(GameState.PRE_GAME)

func _find_bgm_player() -> AudioStreamPlayer:
	# Try to find BGM player in common locations
	var possible_paths = [
		"env/bgm",
		"BGM",
		"Audio/BGM",
		"Music/BGM"
	]
	
	for path in possible_paths:
		var node = get_node_or_null(path)
		if node and node is AudioStreamPlayer:
			return node
	
	# If not found, look for any AudioStreamPlayer in the scene
	var audio_players = get_tree().get_nodes_in_group("bgm")
	if audio_players.size() > 0:
		return audio_players[0]
	
	return null

func select_random_music_set() -> MusicSet:
	if music_sets.size() == 0:
		push_warning("MusicManager: No music sets available")
		return null
	
	var random_index = randi() % music_sets.size()
	_current_music_set = music_sets[random_index]
	music_set_changed.emit(_current_music_set.set_name)
	print("[MusicManager] Selected music set: ", _current_music_set.set_name)
	return _current_music_set

func select_music_set(set_index: int) -> MusicSet:
	if set_index < 0 or set_index >= music_sets.size():
		push_warning("MusicManager: Invalid music set index: ", set_index)
		return null
	
	_current_music_set = music_sets[set_index]
	music_set_changed.emit(_current_music_set.set_name)
	print("[MusicManager] Selected music set: ", _current_music_set.set_name)
	return _current_music_set

func play_state_music(state: GameState) -> void:
	if not _current_music_set:
		push_warning("MusicManager: No music set selected")
		return
	
	if not _bgm_player:
		push_warning("MusicManager: No BGM player available")
		return
	
	var track: AudioStream = null
	var track_name: String = ""
	
	match state:
		GameState.PRE_GAME:
			track = _current_music_set.pre_game_track
			track_name = "Pre-Game"
		GameState.NORMAL_GAME:
			track = _current_music_set.normal_game_track
			track_name = "Normal Game"
		GameState.DOOM:
			track = _current_music_set.doom_track
			track_name = "Doom"
		GameState.OVERWHELM:
			track = _current_music_set.overwhelm_track
			track_name = "Overwhelm"
	
	if not track:
		push_warning("MusicManager: No track available for state: ", state)
		return
	
	_current_state = state
	_change_music_with_fade(track, track_name)

func _change_music_with_fade(new_track: AudioStream, track_name: String) -> void:
	if not _bgm_player:
		return
	
	# Stop any existing fade tween
	if _fade_tween:
		_fade_tween.kill()
	
	_fade_tween = create_tween()
	
	# Fade out current music
	if _bgm_player.playing:
		_fade_tween.tween_property(_bgm_player, "volume_db", -80.0, fade_duration * 0.5)
		await _fade_tween.finished
	
	# Change track and fade in
	_bgm_player.stream = new_track
	_bgm_player.volume_db = -80.0
	_bgm_player.play()
	
	_fade_tween = create_tween()
	_fade_tween.tween_property(_bgm_player, "volume_db", 0.0, fade_duration * 0.5)
	
	music_changed.emit(track_name, _current_state)
	print("[MusicManager] Changed to: ", track_name, " (", _current_music_set.set_name, ")")

func get_current_music_set() -> MusicSet:
	return _current_music_set

func get_current_state() -> GameState:
	return _current_state

func add_music_set(music_set: MusicSet) -> void:
	music_sets.append(music_set)

func remove_music_set(set_index: int) -> void:
	if set_index >= 0 and set_index < music_sets.size():
		music_sets.remove_at(set_index)

func clear_music_sets() -> void:
	music_sets.clear()
	_current_music_set = null 