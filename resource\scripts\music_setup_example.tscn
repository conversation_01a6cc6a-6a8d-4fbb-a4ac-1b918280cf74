[gd_scene load_steps=4 format=3 uid="uid://example_music_setup"]

[ext_resource type="Script" path="res://resource/scripts/music_manager.gd" id="1_music_manager"]
[ext_resource type="Script" path="res://resource/scripts/gamemaster_enhanced.gd" id="2_gamemaster"]
[ext_resource type="Script" path="res://resource/scripts/gamemaster_music_controller.gd" id="3_controller"]

[node name="MusicSystemSetup" type="Node"]

[node name="MusicManager" type="Node" parent="."]
script = ExtResource("1_music_manager")
music_sets = Array[MusicSet]([  ])
fade_duration = 1.0
auto_randomize = true

[node name="GameMaster" type="Node" parent="."]
script = ExtResource("2_gamemaster")
bell_path = NodePath("Bell")
player_path = NodePath("Player")
ui_timer_label = NodePath("UI/TimerLabel")
ui_ms_label = NodePath("UI/MSLabel")
ui_kills_label = NodePath("UI/KillsLabel")
ui_doom_label = NodePath("UI/DoomLabel")
enemy_spawn_zones_path = NodePath("EnemySpawnZones")
bgm_node_path = NodePath("env/bgm")
music_manager = NodePath("../MusicManager")
auto_randomize_music = true
ui_overwhelm_meter = NodePath("UI/OverwhelmMeter")
doom_tick_sound = null
doom_zero_sound = null
in_game_bgm_stream = null
reaper_threshold_speed = 100.0
reaper_time = 2.0
reaper_color = Color(1, 0, 0, 0.6)
ui_reaper_overlay = NodePath("UI/ReaperOverlay")
reaper_sound = null
overwhelm_bgm_stream = null
overwhelm_limit = 30
doom_countdown = 5.0
overwhelm_speed_multiplier = 6.0
global_enemy_cap = 80
max_spawners = 3

[node name="GameMasterMusicController" type="Node" parent="."]
script = ExtResource("3_controller")
music_manager = NodePath("../MusicManager")
gamemaster_path = NodePath("../GameMaster")

[node name="env" type="Node" parent="."]

[node name="bgm" type="AudioStreamPlayer" parent="env"]
bus = "bgm"
autoplay = true

[node name="UI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="TimerLabel" type="RichTextLabel" parent="UI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -100.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 60.0

[node name="MSLabel" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -100.0
offset_top = 60.0
offset_right = -20.0
offset_bottom = 80.0

[node name="KillsLabel" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -100.0
offset_top = 80.0
offset_right = -20.0
offset_bottom = 100.0

[node name="DoomLabel" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -100.0
offset_top = 100.0
offset_right = -20.0
offset_bottom = 120.0

[node name="OverwhelmMeter" type="ProgressBar" parent="UI"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_top = 140.0
offset_right = -20.0
offset_bottom = 160.0

[node name="ReaperOverlay" type="ColorRect" parent="UI"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0)

[node name="EnemySpawnZones" type="Node" parent="."]

[node name="Bell" type="Node" parent="."]

[node name="Player" type="Node" parent="."] 