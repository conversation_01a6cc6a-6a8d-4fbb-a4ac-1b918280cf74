# Setup script for 3D Navigation with Avoidance
# This shows how to configure your 3D scene for proper enemy navigation

extends Node3D

# Example of setting up NavigationObstacle3D for static objects
func setup_wall_obstacle():
	# For a wall or static obstacle
	var wall = StaticBody3D.new()
	var wall_collision = CollisionShape3D.new()
	var wall_mesh = MeshInstance3D.new()
	var nav_obstacle = NavigationObstacle3D.new()
	
	# Create wall collision shape
	var box_shape = BoxShape3D.new()
	box_shape.size = Vector3(4, 3, 0.5)  # 4m wide, 3m tall, 0.5m thick
	wall_collision.shape = box_shape
	
	# Create wall visual
	var box_mesh = BoxMesh.new()
	box_mesh.size = Vector3(4, 3, 0.5)
	wall_mesh.mesh = box_mesh
	
	# Configure navigation obstacle
	nav_obstacle.radius = 2.5  # Slightly larger than half the width
	nav_obstacle.height = 3.0  # Match wall height
	
	# Assemble the wall
	wall.add_child(wall_collision)
	wall.add_child(wall_mesh)
	wall.add_child(nav_obstacle)
	
	add_child(wall)
	return wall

# Example of setting up NavigationObstacle3D for dynamic objects
func setup_dynamic_obstacle():
	# For a moving platform or dynamic obstacle
	var dynamic_obj = RigidBody3D.new()
	var obj_collision = CollisionShape3D.new()
	var obj_mesh = MeshInstance3D.new()
	var nav_obstacle = NavigationObstacle3D.new()
	
	# Create object collision
	var sphere_shape = SphereShape3D.new()
	sphere_shape.radius = 1.0
	obj_collision.shape = sphere_shape
	
	# Create object visual
	var sphere_mesh = SphereMesh.new()
	sphere_mesh.radius = 1.0
	obj_mesh.mesh = sphere_mesh
	
	# Configure navigation obstacle for dynamic object
	nav_obstacle.radius = 1.2  # Slightly larger than object
	nav_obstacle.height = 2.0
	nav_obstacle.avoidance_enabled = true  # Important for dynamic obstacles
	
	# Assemble the dynamic object
	dynamic_obj.add_child(obj_collision)
	dynamic_obj.add_child(obj_mesh)
	dynamic_obj.add_child(nav_obstacle)
	
	add_child(dynamic_obj)
	return dynamic_obj

# Example of creating a Quake Knight enemy
func spawn_knight_enemy(spawn_position: Vector3) -> QuakeKnight3D:
	var knight = QuakeKnight3D.new()
	
	# Set up basic components
	knight.setup_knight()
	knight.global_position = spawn_position
	
	# Configure for your specific needs
	knight.movement_speed = 120.0
	knight.health = 75
	knight.attack_damage = 25
	
	add_child(knight)
	return knight

# Example of setting up NavigationRegion3D with a navmesh
func setup_navigation_region():
	var nav_region = NavigationRegion3D.new()
	var nav_mesh = NavigationMesh.new()
	
	# Configure navigation mesh settings
	nav_mesh.cell_size = 0.25
	nav_mesh.cell_height = 0.2
	nav_mesh.agent_height = 1.8
	nav_mesh.agent_radius = 0.5
	nav_mesh.agent_max_climb = 0.4
	nav_mesh.agent_max_slope = 45.0
	
	# Create a simple floor mesh for navigation
	var floor_mesh = MeshInstance3D.new()
	var plane_mesh = PlaneMesh.new()
	plane_mesh.size = Vector2(20, 20)  # 20x20 meter floor
	floor_mesh.mesh = plane_mesh
	
	# Add floor collision
	var floor_body = StaticBody3D.new()
	var floor_collision = CollisionShape3D.new()
	var floor_shape = BoxShape3D.new()
	floor_shape.size = Vector3(20, 0.1, 20)
	floor_collision.shape = floor_shape
	floor_body.add_child(floor_collision)
	floor_body.add_child(floor_mesh)
	
	# Set up the navigation region
	nav_region.navigation_mesh = nav_mesh
	nav_region.add_child(floor_body)
	
	add_child(nav_region)
	return nav_region

# Complete scene setup example
func _ready():
	# Set up the navigation region first
	var nav_region = setup_navigation_region()
	
	# Add some obstacles
	var wall1 = setup_wall_obstacle()
	wall1.position = Vector3(5, 1.5, 0)
	
	var wall2 = setup_wall_obstacle()
	wall2.position = Vector3(-5, 1.5, 0)
	wall2.rotation_degrees = Vector3(0, 90, 0)
	
	var dynamic_obstacle = setup_dynamic_obstacle()
	dynamic_obstacle.position = Vector3(0, 2, 5)
	
	# Spawn some knight enemies
	spawn_knight_enemy(Vector3(3, 1, 3))
	spawn_knight_enemy(Vector3(-3, 1, -3))
	spawn_knight_enemy(Vector3(0, 1, -5))
	
	# Wait a moment for navigation to initialize, then bake the navmesh
	await get_tree().process_frame
	if nav_region.navigation_mesh:
		nav_region.bake_navigation_mesh()

# Helper function to create a simple player for testing
func create_test_player() -> CharacterBody3D:
	var player = CharacterBody3D.new()
	player.add_to_group("player")
	
	# Add basic collision
	var collision = CollisionShape3D.new()
	var capsule = CapsuleShape3D.new()
	capsule.radius = 0.4
	capsule.height = 1.8
	collision.shape = capsule
	player.add_child(collision)
	
	# Add visual representation
	var mesh_instance = MeshInstance3D.new()
	var capsule_mesh = CapsuleMesh.new()
	capsule_mesh.radius = 0.4
	capsule_mesh.height = 1.8
	mesh_instance.mesh = capsule_mesh
	player.add_child(mesh_instance)
	
	# Basic movement script
	var script_code = """
extends CharacterBody3D

@export var speed = 5.0
@export var gravity = 9.8

func _physics_process(delta):
	if not is_on_floor():
		velocity.y -= gravity * delta
	
	var input_dir = Input.get_vector("ui_left", "ui_right", "ui_up", "ui_down")
	if input_dir != Vector2.ZERO:
		velocity.x = input_dir.x * speed
		velocity.z = input_dir.y * speed
	else:
		velocity.x = move_toward(velocity.x, 0, speed)
		velocity.z = move_toward(velocity.z, 0, speed)
	
	move_and_slide()

func take_damage(amount: int):
	print("Player takes ", amount, " damage!")
"""
	
	var player_script = GDScript.new()
	player_script.source_code = script_code
	player.set_script(player_script)
	
	player.position = Vector3(0, 1, 0)
	add_child(player)
	return player
